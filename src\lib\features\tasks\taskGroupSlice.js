import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	taskGroups: [],
	isLoading: false,
	error: null,
};

export const fetchTaskGroups = createAsyncThunk(
	'taskGroup/fetchTaskGroups',
	async (projectId, { rejectWithValue }) => {
		try {
			const { data } = await customFetch.get(`/task-groups/project/${projectId}`);
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const createTaskGroup = createAsyncThunk(
	'taskGroup/createTaskGroup',
	async ({ projectId, taskGroupData }, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.post('/task-groups', taskGroupData);
			if (data.success) {
				dispatch(fetchTaskGroups(projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const updateTaskGroup = createAsyncThunk(
	'taskGroup/updateTaskGroup',
	async ({ projectId, taskGroupData }, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.patch('/task-groups', taskGroupData);
			if (data.success) {
				dispatch(fetchTaskGroups(projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

export const deleteTaskGroup = createAsyncThunk(
	'taskGroup/deleteTaskGroup',
	async ({ projectId, taskGroupId }, { rejectWithValue, dispatch }) => {
		try {
			const { data } = await customFetch.delete(`/task-groups/${taskGroupId}`);
			if (data.success) {
				dispatch(fetchTaskGroups(projectId));
			}
			return data;
		} catch (error) {
			return rejectWithValue(error.response.data);
		}
	}
);

const taskGroupSlice = createSlice({
	name: 'taskGroup',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchTaskGroups.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchTaskGroups.fulfilled, (state, action) => {
				state.isLoading = false;
				state.taskGroups = action.payload.data;
			})
			.addCase(fetchTaskGroups.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload;
			})
			.addCase(createTaskGroup.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(createTaskGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(createTaskGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateTaskGroup.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(updateTaskGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateTaskGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteTaskGroup.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(deleteTaskGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteTaskGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export default taskGroupSlice.reducer;
