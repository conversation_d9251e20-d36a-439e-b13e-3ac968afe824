import { countryDataFormats } from '../utils';

const { z } = require('zod');

export const onboardingSchema = z.object({
	businessName: z
		.string()
		.min(2, 'Business name must be at least 2 characters'),
	businessCountry: z.string().min(2, 'Please select a country'),
	registration: z.string().min(2, 'Registration number is required'),
	address: z.string().min(5, 'Address must be at least 5 characters'),
	currency: z.string().min(1, 'Please select a currency'),
	timeFormat: z.enum(['12', '24'], {
		required_error: 'Please select a time format',
	}),
	dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY/MM/DD'], {
		required_error: 'Please select a date format',
	}),
	branches: z
		.array(
			z.object({
				name: z.string().min(2, 'Branch name must be at least 2 characters'),
				location: z
					.string()
					.min(5, 'Branch location must be at least 5 characters'),
			})
		)
		.optional()
		.default([]),
})

export const onboardingStepOneSchema = z
	.object({
		businessName: z
			.string()
			.nonempty('Business name is required')
			.min(4, { message: 'Business name must be at least 4 characters long' }),
		businessCountry: z
			.string()
			.nonempty('Business country is required')
			.min(3, {
				message: 'Business country must be at least 3 characters long',
			}),
		businessCountryDialCode: z
			.string()
			.nonempty('Business country code is required')
			.min(2, {
				message: 'Business country code must be at least 2 characters long',
			})
			.regex(/^\+\d{1,3}$/, {
				message:
					'Business country code must be a valid international dialing code (e.g., +65, +91)',
			}),
		registration: z
			.string()
			.nonempty('Registration is required')
			.min(6, { message: 'Registration must be at least 6 characters long' })
			.regex(/^[a-zA-Z0-9]+$/, {
				message: 'Registration should only contain letters and numbers',
			}),
		address: z
			.string()
			.nonempty('Address is required')
			.min(2, { message: 'Address must be at least 2 characters long' }),
	}).superRefine((data, ctx) => {
	if(data.businessCountry){
		const country = countryDataFormats.find(
			(country) => country.country === data.businessCountry
		)
		if(!country.registration.regex.test(data.registration)){
			ctx.addIssue({
				code: 'custom',
				message: 'Registration number does not match country format',
				path : ['registration']
			})
		}
	}
});

const timeFormatEnum = ['12h', '24h'];
const dateFormatEnum = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'];

export const onboardingFinalStepSchema = z.object({
	currency: z
		.string()
		.nonempty('Currency is required')
		.min(2, { message: 'Currency must be at least 2 characters long' })
		.regex(/^[a-zA-Z\s]+$/, {
			message: 'Currency must contain only letters and spaces',
		}),

	timeFormat: z
		.string()
		.nonempty('Time Format is required')
		.min(2, { message: 'Time Format must be at least 2 characters long' })
		.refine(
			(format) => {
				return timeFormatEnum.includes(format);
			},
			{
				message: `Time Format must be one of the following: ${timeFormatEnum.join(
					', '
				)}`,
			}
		),

	dateFormat: z
		.string()
		.nonempty('Date Format is required')
		.min(2, { message: 'Date Format must be at least 10 characters long' })
		.refine((format) => dateFormatEnum.includes(format), {
			message: `Date Format must be one of the following: ${dateFormatEnum.join(
				', '
			)}`,
		}),
});

export const branchesSchema = z.object({
	branches: z
		.array(
			z.object({
				branchName: z.string().nonempty('Branch name is required').min(2, {
					message: 'Branch name must be at least 2 characters long',
				}),
				branchLocation: z
					.string()
					.nonempty('Branch location is required')
					.min(2, {
						message: 'Branch location must be at least 2 characters long',
					}),
				// departments: z.array(
				// 	z.object({
				// 		departmentName: z
				// 			.string()
				// 			.nonempty('Department name is required')
				// 			.min(2, {
				// 				message: 'Department name must be at least 2 characters long',
				// 			}),
				// 	})
				// ),
			})
		)
		.max(3, 'You can have up to 3 branches'),
});
