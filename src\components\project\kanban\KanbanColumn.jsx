'use client';

import React, { useState } from 'react';
import { Droppable } from '@hello-pangea/dnd';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';
import {
	Plus,
	MoreHorizontal,
	Check,
	ChevronLeft,
	ChevronRight,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { KanbanCard } from './KanbanCard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TaskModal } from './TaskModal';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

// Task creation schema (simplified for quick task creation)
const quickTaskSchema = z.object({
	name: z
		.string()
		.min(1, 'Task name is required')
		.max(50, 'Task name must be at most 50 characters'),
});

/**
 * KanbanColumn Component
 * Represents a group in the kanban board (e.g., To Do, In Progress, Done)
 */
export const KanbanColumn = ({
	column,
	cards = [],
	onCardClick,
	onAddCard,
	onColumnMenuClick,
	isDropTarget = false,
	isGlassMode = false,
	isDropdownOpen = false,
	onDropdownOpenChange,
	className,
	...props
}) => {
	const [isAddingTask, setIsAddingTask] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [editTitle, setEditTitle] = useState(column.name);
	const [isCollapsed, setIsCollapsed] = useState(false);
	const [selectedTask, setSelectedTask] = useState(null);
	const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
	const [selectedColor, setSelectedColor] = useState(
		column.bgColor ||
			(isGlassMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.9)')
	);

	// React Hook Form for quick task creation
	const taskForm = useForm({
		resolver: zodResolver(quickTaskSchema),
		defaultValues: {
			name: '',
		},
	});

	// Color options for column - 8 pastel colors
	const colorOptions = [
		{ name: 'Header White', value: 'rgba(255, 255, 255, 0.9)' },
		{ name: 'Soft Pink', value: '#fce7f3' },
		{ name: 'Soft Orange', value: '#fed7aa' },
		{ name: 'Soft Yellow', value: '#fef3c7' },
		{ name: 'Soft Green', value: '#d1fae5' },
		{ name: 'Soft Blue', value: '#dbeafe' },
		{ name: 'Soft Purple', value: '#e9d5ff' },
		{ name: 'Soft Teal', value: '#ccfbf1' },
	];

	// Form submission handler
	const onSubmitTask = (data) => {
		onAddCard?.(column._id, data);
		setIsAddingTask(false);
		taskForm.reset();
	};

	const handleEditKeyPress = (e) => {
		if (e.key === 'Enter') {
			handleSaveEdit();
		} else if (e.key === 'Escape') {
			setIsEditing(false);
			setEditTitle(column.name);
		}
	};

	const handleSaveEdit = () => {
		if (editTitle.trim() && editTitle !== column.name) {
			// TODO: Call onColumnUpdate callback with new title
			console.log('Update column title:', editTitle);
		}
		setIsEditing(false);
	};

	const handleColorChange = (color) => {
		setSelectedColor(color);
		// TODO: Call onColumnUpdate callback with new color
		console.log('Update column color:', color);
	};

	const handleCardClick = (card) => {
		setSelectedTask(card);
		setIsTaskModalOpen(true);
		onCardClick?.(card);
	};

	const handleTaskModalClose = () => {
		setIsTaskModalOpen(false);
		setSelectedTask(null);
	};

	const handleTaskSave = (updatedTask) => {
		// TODO: Update task in the column data
		console.log('Task saved:', updatedTask);
		// In a real app, this would call an API or update state
	};

	return (
		<div
			className={cn(
				// Base column styling following design system with responsive widths
				'flex-shrink-0 h-fit transition-all duration-300',
				isCollapsed ? 'w-12' : 'w-[17rem]', // Collapsed: 48px, Expanded: 272px
				className
			)}
			{...props}
		>
			<Card
				className={cn(
					// Conditional styling based on glass mode
					isGlassMode ? 'bg-white/10 backdrop-blur-md border ' : '',
					'shadow-sm transition-all duration-300 flex flex-col gap-2 dark:bg-background',
					isCollapsed ? 'p-1' : 'p-2'
				)}
				style={{
					backgroundColor: !isGlassMode ? selectedColor : undefined,
				}}
				role="region"
				aria-label={`${column.name} group with ${cards.length} tasks`}
			>
				{/* Column Header */}
				<CardHeader className={cn('transition-all duration-300 p-2')}>
					{isCollapsed ? (
						// Collapsed Header - Vertical Layout
						<div className="flex flex-col items-center gap-2 h-full">
							{/* Collapse/Expand Button */}
							<Button
								variant="ghost"
								size="sm"
								onClick={() => setIsCollapsed(false)}
								className={cn(
									'h-6 w-6 p-0 transition-all duration-200',
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
								)}
								title="Expand column"
							>
								<ChevronRight className="h-4 w-4" />
							</Button>

							{/* Vertical Title */}
							<div className="flex flex-col items-center gap-2 flex-1">
								<div
									className={cn(
										'text-sm font-semibold transition-colors duration-200 writing-mode-vertical-rl text-orientation-mixed',
										isGlassMode ? 'text-white' : 'text-gray-700'
									)}
									style={{
										writingMode: 'vertical-rl',
										textOrientation: 'mixed',
									}}
									title={column.name}
								>
									{column.name}
								</div>

								{/* Count Badge */}
								<Badge
									variant="secondary"
									className={cn(
										'text-xs px-1 py-0.5 transition-all duration-200 min-w-[20px] text-center',
										isGlassMode
											? 'bg-white/20 text-white/80'
											: 'bg-gray-200 text-gray-600'
									)}
								>
									{cards.length}
								</Badge>
							</div>
						</div>
					) : (
						// Expanded Header - Horizontal Layout
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								{/* Collapse Button */}
								<Button
									variant="ghost"
									size="sm"
									onClick={() => setIsCollapsed(true)}
									className={cn(
										'h-6 w-6 p-0 transition-all duration-200',
										isGlassMode
											? 'text-white/70 hover:text-white hover:bg-white/20'
											: 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
									)}
									title="Collapse column"
								>
									<ChevronLeft className="h-4 w-4" />
								</Button>

								<h3
									className={cn(
										'text-sm font-semibold transition-colors duration-200',
										isGlassMode
											? 'text-white group-hover:text-white/90'
											: 'text-gray-700 group-hover:text-gray-900'
									)}
								>
									{column.name}
								</h3>
								<Badge
									variant="secondary"
									className={cn(
										'text-xs px-2 py-0.5 transition-all duration-200',
										isGlassMode
											? 'bg-white/20 text-white/80 hover:bg-white/30'
											: 'bg-gray-200 text-gray-600 hover:bg-gray-300'
									)}
								>
									{cards.length}
								</Badge>
							</div>

							<DropdownMenu
								open={isDropdownOpen}
								onOpenChange={onDropdownOpenChange}
							>
								<DropdownMenuTrigger asChild>
									<Button
										variant="ghost"
										size="sm"
										className={cn(
											'h-6 w-6 p-0 transition-all duration-200',
											isGlassMode
												? 'text-white/70 hover:text-white hover:bg-white/20'
												: 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
										)}
									>
										<MoreHorizontal className="h-4 w-4" />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-64">
									{/* Editable Title Input */}
									<div className="px-3 py-2">
										{isEditing ? (
											<div className="space-y-2">
												<Input
													value={editTitle}
													onChange={(e) => setEditTitle(e.target.value)}
													onKeyDown={handleEditKeyPress}
													onBlur={handleSaveEdit}
													className="h-8 text-sm"
													autoFocus
												/>
												<div className="flex gap-1">
													<Button
														size="sm"
														onClick={handleSaveEdit}
														className="h-6 px-2 text-xs"
													>
														<Check className="h-3 w-3" />
													</Button>
													<Button
														variant="ghost"
														size="sm"
														onClick={() => {
															setIsEditing(false);
															setEditTitle(column.name);
														}}
														className="h-6 px-2 text-xs"
													>
														Cancel
													</Button>
												</div>
											</div>
										) : (
											<Button
												variant="ghost"
												size="sm"
												onClick={() => setIsEditing(true)}
												className="h-8 w-full justify-start text-sm px-2"
											>
												{column.name}
											</Button>
										)}
									</div>

									<DropdownMenuSeparator />

									{/* Color Section */}
									<div className="p-2">
										<div className="grid grid-cols-4 gap-2">
											{colorOptions.map((color) => (
												<button
													key={color.value}
													onClick={() => handleColorChange(color.value)}
													className={cn(
														'w-12 h-6 rounded border-2 transition-all duration-200 hover:scale-105',
														selectedColor === color.value
															? 'border-gray-900 ring-2 ring-gray-300'
															: 'border-gray-200 hover:border-gray-300'
													)}
													style={{ backgroundColor: color.value }}
													title={color.name}
												/>
											))}
										</div>
									</div>
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					)}
				</CardHeader>

				{/* Tasks Container - Only show when expanded */}
				{!isCollapsed && (
					<>
						<CardContent className="space-y-0 pt-0 p-0">
							{/* Existing Tasks */}
							<Droppable droppableId={column._id}>
								{(provided, snapshot) => (
									<div
										ref={provided.innerRef}
										{...provided.droppableProps}
										className={cn(
											'space-y-0 min-h-[1px] transition-colors duration-200 gap-2 flex flex-col',
											snapshot.isDraggingOver &&
												(isGlassMode
													? 'bg-white/20 rounded-md'
													: 'bg-blue-50/50 rounded-md')
										)}
									>
										{cards.map((task, index) => (
											<KanbanCard
												key={task._id}
												card={task}
												index={index}
												onClick={handleCardClick}
											/>
										))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</CardContent>

						{/* Add New Task */}
						<div className="flex-shrink-0">
							{isAddingTask ? (
								<Card className="bg-white border border-gray-200 shadow-sm">
									<CardContent className="p-3">
										<Form {...taskForm}>
											<form
												onSubmit={taskForm.handleSubmit(onSubmitTask)}
												className="space-y-2"
											>
												<FormField
													control={taskForm.control}
													name="name"
													render={({ field }) => (
														<FormItem>
															<FormControl>
																<textarea
																	{...field}
																	placeholder="Enter a title for this task..."
																	className="w-full text-sm border-none outline-none resize-none bg-transparent placeholder-gray-500 focus:placeholder-gray-400 transition-colors"
																	rows={2}
																	autoFocus
																	onKeyDown={(e) => {
																		if (e.key === 'Enter' && !e.shiftKey) {
																			e.preventDefault();
																			taskForm.handleSubmit(onSubmitTask)();
																		} else if (e.key === 'Escape') {
																			setIsAddingTask(false);
																			taskForm.reset();
																		}
																	}}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
												<div className="flex items-center gap-2">
													<Button
														type="submit"
														size="sm"
														disabled={!taskForm.watch('name')?.trim()}
														className="h-7 px-3 text-xs bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
													>
														Add task
													</Button>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														onClick={() => {
															setIsAddingTask(false);
															taskForm.reset();
														}}
														className="h-7 px-3 text-xs text-gray-600 hover:text-gray-800 transition-colors duration-200"
													>
														Cancel
													</Button>
												</div>
											</form>
										</Form>
									</CardContent>
								</Card>
							) : (
								<Button
									variant="ghost"
									className={cn(
										'w-full justify-start text-left p-2 h-auto',
										'border-2 border-dashed transition-all duration-200 hover:scale-[1.02]',
										'bg-transparent',
										isGlassMode
											? 'text-white/70 border-white/30 hover:border-white/50 hover:bg-white/10'
											: 'text-gray-500 hover:text-gray-700 border-gray-300 hover:border-gray-400 hover:bg-gray-50'
									)}
									onClick={() => setIsAddingTask(true)}
								>
									<Plus className="h-4 w-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
									<span className="text-sm">Add a task</span>
								</Button>
							)}
						</div>
					</>
				)}
			</Card>

			{/* Task Modal */}
			<TaskModal
				isOpen={isTaskModalOpen}
				onClose={handleTaskModalClose}
				task={selectedTask}
				onSave={handleTaskSave}
				isGlassMode={isGlassMode}
			/>
		</div>
	);
};
