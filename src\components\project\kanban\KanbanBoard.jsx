'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { DragDropContext } from '@hello-pangea/dnd';
import { cn } from '@/lib/utils';
import { KanbanHeader } from './KanbanHeader';
import { KanbanColumn } from './KanbanColumn';
import { sampleProjects } from '../data/sample-projects';
import { getKanbanDataByProjectId } from '../data/kanban-data';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchTaskBoardOfProject,
	createTask,
	updateTask,
	createTaskGroup,
	updateTaskGroup,
} from '@/lib/features/tasks/tasksSlice';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from '@/components/ui/form';

// Import schemas from root schema.js - these should match the schemas in schema.js
const createTaskGroupSchema = z.object({
	name: z.string().nonempty('Task group name is required'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	companyId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Company is required'),
});

const createTaskSchema = z.object({
	name: z
		.string()
		.nonempty('Task name is required')
		.max(50, 'Task name must be at most 50 characters long'),
	code: z
		.string()
		.nonempty('Project code is required')
		.max(6, 'Project code must be at most 6 characters long'),
	projectId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Project is required'),
	groupId: z
		.string()
		.regex(/^[a-f\d]{24}$/i, 'Invalid ID')
		.nonempty('Group is required'),
});

/**
 * KanbanBoard Component
 * Main kanban board container with horizontal scrolling groups
 */
export const KanbanBoard = ({
	projectId,
	project,
	isGlassMode = false,
	className,
	...props
}) => {
	const router = useRouter();
	const dispatch = useAppDispatch();
	const { taskBoard, isLoading } = useAppSelector((store) => store.tasks);
	// const [project, setProject] = useState(null);
	const [kanbanData, setKanbanData] = useState({});
	const [openDropdownId, setOpenDropdownId] = useState(null);
	const [isAddingGroup, setIsAddingGroup] = useState(false);

	// React Hook Form for group creation
	const groupForm = useForm({
		resolver: zodResolver(createTaskGroupSchema),
		defaultValues: {
			name: '',
			projectId: projectId,
			companyId: project?.companyId || '',
		},
	});

	useEffect(() => {
		dispatch(fetchTaskBoardOfProject(projectId));
		setKanbanData(taskBoard);
	}, [dispatch, projectId, taskBoard]);

	// Drag and drop event handlers for @hello-pangea/dnd
	const handleDragEnd = (result) => {
		const { destination, source } = result;

		// If no destination, do nothing
		if (!destination) return;

		// If dropped in the same position, do nothing
		if (
			destination.droppableId === source.droppableId &&
			destination.index === source.index
		) {
			return;
		}

		const sourceGroupId = source.droppableId;
		const destinationGroupId = destination.droppableId;

		// Moving within the same group
		if (sourceGroupId === destinationGroupId) {
			const group = kanbanData.groups.find((grp) => grp._id === sourceGroupId);
			if (!group) return;

			const newTasks = Array.from(group.tasks);
			const [movedTask] = newTasks.splice(source.index, 1);
			newTasks.splice(destination.index, 0, movedTask);

			setKanbanData((prevData) => ({
				...prevData,
				groups: prevData.groups.map((grp) =>
					grp._id === sourceGroupId ? { ...grp, tasks: newTasks } : grp
				),
			}));
		} else {
			// Moving between different groups
			const sourceGroup = kanbanData.groups.find(
				(grp) => grp._id === sourceGroupId
			);
			const destinationGroup = kanbanData.groups.find(
				(grp) => grp._id === destinationGroupId
			);

			if (!sourceGroup || !destinationGroup) return;

			const sourceTasks = Array.from(sourceGroup.tasks);
			const destinationTasks = Array.from(destinationGroup.tasks);
			const [movedTask] = sourceTasks.splice(source.index, 1);
			destinationTasks.splice(destination.index, 0, movedTask);

			// Update the task's groupId when moving between groups
			const updatedTask = { ...movedTask, groupId: destinationGroupId };

			// Update local state
			setKanbanData((prevData) => ({
				...prevData,
				groups: prevData.groups.map((grp) => {
					if (grp._id === sourceGroupId) {
						return { ...grp, tasks: sourceTasks };
					}
					if (grp._id === destinationGroupId) {
						return {
							...grp,
							tasks: [
								...destinationTasks.slice(0, destination.index),
								updatedTask,
								...destinationTasks.slice(destination.index),
							],
						};
					}
					return grp;
				}),
			}));

			// Update task in backend
			dispatch(
				updateTask({
					projectId,
					taskData: {
						taskId: movedTask._id,
						groupId: destinationGroupId,
					},
				})
			);
		}
	};

	const handleTaskClick = (task) => {
		// TODO: Open task detail modal
		console.log('Task clicked:', task);
	};

	const handleAddTask = (groupId, taskData) => {
		// Create task data with form validation
		const formData = {
			name: taskData.name,
			code: project?.code || 'TASK',
			projectId: projectId,
			groupId: groupId,
		};

		// Validate using the schema and dispatch
		try {
			const validatedData = createTaskSchema.parse(formData);

			// Dispatch create task action
			dispatch(
				createTask({
					projectId,
					taskData: validatedData,
				})
			);
		} catch (error) {
			console.error('Task validation failed:', error);
			// Handle validation errors - could show toast or form errors
		}
	};

	const handleGroupMenuClick = (group) => {
		// TODO: Implement group menu
		console.log('Group menu clicked:', group);
	};

	if (isLoading) {
		return (
			<div className="min-h-screen items-center justify-center">
				<div className="text-center">
					<div className="inline-flex items-center gap-3 text-white">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
						<span className="text-lg animate-pulse">
							isLoading kanban board...
						</span>
					</div>
				</div>
			</div>
		);
	}

	if (
		!project
		//  || !kanbanData
	) {
		return (
			<div className="min-h-screen bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center">
				<div className="text-white text-lg">Project not found</div>
			</div>
		);
	}

	return (
		<DragDropContext onDragEnd={handleDragEnd}>
			<div className="flex gap-3 min-w-max">
				{kanbanData?.groups?.map((group) => (
					<div
						key={group._id}
						// className="animate-in slide-in-from-bottom duration-300"
						// style={{ animationDelay: `${index * 100}ms` }}
					>
						<KanbanColumn
							column={group}
							cards={group.tasks}
							onCardClick={handleTaskClick}
							onAddCard={handleAddTask}
							onColumnMenuClick={handleGroupMenuClick}
							isGlassMode={isGlassMode}
							isDropdownOpen={openDropdownId === group._id}
							onDropdownOpenChange={(isOpen) =>
								setOpenDropdownId(isOpen ? group._id : null)
							}
						/>
					</div>
				))}

				{/* Add a Group */}
				<div className="w-[17rem] flex-shrink-0">
					<Card
						className={cn(
							// Conditional styling based on glass mode
							isGlassMode
								? 'bg-white/10 backdrop-blur-md border border-white/20'
								: 'border-gray-200',
							'shadow-sm p-3 transition-all duration-300'
						)}
						style={{
							backgroundColor: !isGlassMode
								? 'rgba(255, 255, 255, 0.9)'
								: undefined,
						}}
					>
						{isAddingGroup ? (
							<Form {...groupForm}>
								<form
									onSubmit={groupForm.handleSubmit((data) => {
										dispatch(
											createTaskGroup({
												projectId,
												taskGroupData: data,
											})
										);
										setIsAddingGroup(false);
										groupForm.reset();
									})}
									className="space-y-2"
								>
									<FormField
										control={groupForm.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter group title..."
														className="h-8 text-sm"
														autoFocus
														onKeyDown={(e) => {
															if (e.key === 'Enter') {
																e.preventDefault();
																groupForm.handleSubmit((data) => {
																	dispatch(
																		createTaskGroup({
																			projectId,
																			taskGroupData: data,
																		})
																	);
																	setIsAddingGroup(false);
																	groupForm.reset();
																})();
															} else if (e.key === 'Escape') {
																setIsAddingGroup(false);
																groupForm.reset();
															}
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<div className="flex gap-2">
										<Button
											type="submit"
											size="sm"
											disabled={!groupForm.watch('name')?.trim()}
											className="h-7 px-3 text-xs"
										>
											Add Group
										</Button>
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={() => {
												setIsAddingGroup(false);
												groupForm.reset();
											}}
											className="h-7 px-2 text-xs"
										>
											<X className="h-3 w-3" />
										</Button>
									</div>
								</form>
							</Form>
						) : (
							<Button
								variant="ghost"
								onClick={() => setIsAddingGroup(true)}
								className={cn(
									'w-full justify-start text-left h-8',
									isGlassMode
										? 'text-white/70 hover:text-white hover:bg-white/20'
										: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
								)}
							>
								<Plus className="h-4 w-4 mr-2" />
								Add a group
							</Button>
						)}
					</Card>
				</div>
			</div>
		</DragDropContext>
	);
};
